<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { resolveParam } from '@haierbusiness-front/utils';
import schemeDetails from '../../scheme/schemeDetails.vue';
import schemeChange from '../../scheme/schemeChange.vue';
import billUploadScheme from '@haierbusiness-front/components/billUploadScheme/billUploadschemeInteract.vue';

const route = useRoute();
const viewSelect = ref('demand');
const routeParams = ref<any>({});

onMounted(() => {
  // 获取路由参数
  if (route.query.record) {
    routeParams.value = resolveParam(route.query.record);

    // 根据 schemeType 自动切换视图
    const schemeType = routeParams.value.schemeType;
    if (schemeType === 'billUpload') {
      viewSelect.value = 'billUpload';
    } else if (schemeType === 'schemeView' || schemeType === 'reported') {
      viewSelect.value = 'scheme';
    } else {
      viewSelect.value = 'demand';
    }
  }
});
</script>
<template>
  <div class="container">
    <schemeDetails v-if="viewSelect === 'demand'" v-bind="routeParams"> </schemeDetails>
    <schemeChange v-else-if="viewSelect === 'scheme'" v-bind="routeParams"> </schemeChange>
    <billUploadScheme v-else-if="viewSelect === 'billUpload'" v-bind="routeParams"> </billUploadScheme>
    <div class="footer">
      <a-radio-group v-model:value="viewSelect">
        <a-radio-button value="demand">需求视图</a-radio-button>
        <a-radio-button value="scheme">方案视图</a-radio-button>
        <a-radio-button value="billUpload">账单视图</a-radio-button>
      </a-radio-group>
      <a-button type="primary">返回</a-button>
    </div>
  </div>
</template>
<style lang="less" scoped>
.container {
  padding-bottom: 40px;
  width: 100%;
  height: 100%;
  position: relative;
}
.footer {
  right: 0;
  background: #fff;
  z-index: 11;
  width: calc(100% - 250px);
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}
</style>
